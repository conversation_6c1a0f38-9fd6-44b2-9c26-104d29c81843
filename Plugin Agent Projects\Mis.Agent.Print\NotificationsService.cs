﻿using Mis.Shared.Interface;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Print
{
    [Plugin("Notifications", Version = "1.0.0", Description = "Notifications management", Order = 40)]

    public class NotificationsService : INotificationAppService
    {
        private string _databaseFile;
        private bool _notificationsEnabled = false;
        private readonly object _lock = new object();

        //public event Action NotificationUpdated;

        public NotificationsService()
        {
            var baseDirectory = AppContext.BaseDirectory;
            _databaseFile = Path.Combine(baseDirectory, "notifications.db");

            EnsureDatabaseAndTables();
            NotificationManager.NotificationEvent += OnNotificationReceived;
        }

        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }
        [PluginNotificationHandler]

        public void ShowNotification(string title, string text)
        {
            if (_notificationsEnabled)
            {
                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = text
                })
                {
                    notifyIcon.ShowBalloonTip(1000);
                    Task.Delay(1000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
        }
        [PluginTabProvider]

        public object GetTabPage()
        {
            var notificationsForm = new NotificationsForm(this);
            notificationsForm.PopulateForm();
            return notificationsForm.NotificationsTab;
        }

        private void EnsureDatabaseAndTables()
        {

            if (!File.Exists(_databaseFile))
            {
                // Create the database file
                SQLiteConnection.CreateFile(_databaseFile);
            }

            // Create tables if they don't exist
            using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
            {
                connection.Open();

                var createNotificationsTableQuery = @"
                CREATE TABLE IF NOT EXISTS Notifications (
                    Id TEXT PRIMARY KEY,
                    No TEXT NOT NULL,
                    HtmlContent TEXT,
                    IsPrinted INTEGER NOT NULL,
                    ReceiveTime DATETIME NOT NULL
                )";

                using (var command = new SQLiteCommand(createNotificationsTableQuery, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        public bool DatabaseFileExists() => File.Exists(_databaseFile);

        public string GetDatabasePath() => _databaseFile;



        public bool TableExists(string tableName)
        {
            using (var connection = new SQLiteConnection($"Data Source={_databaseFile};Version=3;"))
            {
                connection.Open();
                var query = $"SELECT name FROM sqlite_master WHERE type='table' AND name='{tableName}';";

                using (var command = new SQLiteCommand(query, connection))
                {
                    var result = command.ExecuteScalar();
                    return result != null && result.ToString() == tableName;
                }
            }
        }



    

        public async Task ClearAllNotificationsAsync()
        {
            await Task.Run(() =>
            {
                lock (_lock)
                {
                    try
                    {
                        using (var connection = new SQLiteConnection($"Data Source={_databaseFile}"))
                        {
                            connection.Open();

                            // Clear all notifications from the table
                            var command = new SQLiteCommand("DELETE FROM notifications", connection);
                            command.ExecuteNonQuery();
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log error or handle as needed
                        throw new InvalidOperationException($"Failed to clear notifications: {ex.Message}", ex);
                    }
                }
            });
        }
    }
}
