{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Mis Agent (Debug)",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/Mis.Agent/bin/Debug/net9.0-windows/Mis.Agent.dll",
      "args": [],
      "cwd": "${workspaceFolder}/Mis.Agent",
      "stopAtEntry": false,
      "console": "externalTerminal",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "ASPNETCORE_URLS": "http://localhost:7000"
      },
      "logging": {
        "moduleLoad": false
      }
    },
    {
      "name": "Launch Mis Agent (Console Debug)",
      "type": "coreclr",
      "request": "launch",
      "preLaunchTask": "build",
      "program": "${workspaceFolder}/Mis.Agent/bin/Debug/net9.0-windows/Mis.Agent.dll",
      "args": [],
      "cwd": "${workspaceFolder}/Mis.Agent",
      "stopAtEntry": false,
      "console": "integratedTerminal",
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development",
        "ASPNETCORE_URLS": "http://localhost:7000"
      },
      "logging": {
        "moduleLoad": false
      }
    },
    {
      "name": "Attach to Mis Agent",
      "type": "coreclr",
      "request": "attach",
      "processName": "Mis.Agent"
    }
  ]
}
