using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Threading.Tasks;
using System.Runtime.InteropServices;

namespace Mis.Agent.Barcode.Services
{
    public class WiaImageCaptureService
    {
        public async Task<string> CaptureImageAsync()
        {
            return await Task.Run(() =>
            {
                try
                {
                    Console.WriteLine("[DEBUG] Starting WIA image capture...");

                    // Try to use WIA.CommonDialog
                    Type wiaCommonDialogType = Type.GetTypeFromProgID("WIA.CommonDialog");
                    if (wiaCommonDialogType == null)
                    {
                        Console.WriteLine("[DEBUG] WIA.CommonDialog not available");
                        return TryAlternativeWiaCapture();
                    }

                    dynamic commonDialog = Activator.CreateInstance(wiaCommonDialogType);

                    Console.WriteLine("[DEBUG] WIA CommonDialog created");

                    // Show device selection dialog
                    dynamic device = commonDialog.ShowSelectDevice();
                    if (device == null)
                    {
                        Console.WriteLine("[DEBUG] No WIA device selected");
                        return null;
                    }

                    Console.WriteLine($"[DEBUG] Selected device: {device.Properties["Name"].Value}");

                    // Get scanner item (usually the first item)
                    dynamic item = device.Items[1]; // Index 1 is typically the scanner

                    if (item == null)
                    {
                        Console.WriteLine("[DEBUG] No scanner item found");
                        return null;
                    }

                    Console.WriteLine("[DEBUG] Found scanner item, capturing image...");

                    // Show acquire image dialog
                    dynamic imageFile = commonDialog.ShowAcquireImage();

                    if (imageFile == null)
                    {
                        Console.WriteLine("[DEBUG] No image acquired");
                        return null;
                    }

                    Console.WriteLine("[DEBUG] Image acquired, converting to Base64...");

                    // Convert to Base64
                    string tempPath = Path.GetTempFileName() + ".bmp";
                    imageFile.SaveFile(tempPath);

                    if (File.Exists(tempPath))
                    {
                        byte[] imageBytes = File.ReadAllBytes(tempPath);
                        string base64 = Convert.ToBase64String(imageBytes);

                        // Clean up
                        try { File.Delete(tempPath); } catch { }

                        Console.WriteLine($"[DEBUG] ✓ WIA capture successful! Image size: {imageBytes.Length} bytes");
                        return base64;
                    }

                    return null;
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[DEBUG] WIA Exception: {ex.Message}");
                    return TryAlternativeWiaCapture();
                }
            });
        }

        private string TryAlternativeWiaCapture()
        {
            try
            {
                Console.WriteLine("[DEBUG] Trying alternative WIA capture method...");

                // Try WIA.DeviceManager
                Type deviceManagerType = Type.GetTypeFromProgID("WIA.DeviceManager");
                if (deviceManagerType == null)
                {
                    Console.WriteLine("[DEBUG] WIA.DeviceManager not available");
                    return null;
                }

                dynamic deviceManager = Activator.CreateInstance(deviceManagerType);

                // Get device collection
                dynamic deviceInfos = deviceManager.DeviceInfos;

                if (deviceInfos.Count == 0)
                {
                    Console.WriteLine("[DEBUG] No WIA devices found");
                    return null;
                }

                Console.WriteLine($"[DEBUG] Found {deviceInfos.Count} WIA devices");

                // Try each device
                for (int i = 1; i <= deviceInfos.Count; i++)
                {
                    try
                    {
                        dynamic deviceInfo = deviceInfos[i];
                        Console.WriteLine($"[DEBUG] Trying device: {deviceInfo.Properties["Name"].Value}");

                        dynamic device = deviceInfo.Connect();

                        // Get the first item (scanner)
                        if (device.Items.Count > 0)
                        {
                            dynamic item = device.Items[1];

                            // Try to transfer image
                            dynamic imageFile = item.Transfer();

                            if (imageFile != null)
                            {
                                string tempPath = Path.GetTempFileName() + ".bmp";
                                imageFile.SaveFile(tempPath);

                                if (File.Exists(tempPath))
                                {
                                    byte[] imageBytes = File.ReadAllBytes(tempPath);
                                    string base64 = Convert.ToBase64String(imageBytes);

                                    // Clean up
                                    try { File.Delete(tempPath); } catch { }

                                    Console.WriteLine($"[DEBUG] ✓ Alternative WIA capture successful!");
                                    return base64;
                                }
                            }
                        }
                    }
                    catch (Exception deviceEx)
                    {
                        Console.WriteLine($"[DEBUG] Device {i} failed: {deviceEx.Message}");
                        continue;
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] Alternative WIA capture failed: {ex.Message}");
                return null;
            }
        }
    }
}

[ComImport]
[Guid("79C07CF1-CBDD-41EE-8EC3-F00080CADA7A")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IWiaDevMgr2
{
    void SelectDeviceDlg([In] int lFlags, [In, MarshalAs(UnmanagedType.BStr)] string bstrDeviceType, [In] int lDeviceType, [Out, MarshalAs(UnmanagedType.BStr)] out string pbstrDeviceID, [Out, MarshalAs(UnmanagedType.Interface)] out IWiaItem2 ppItemRoot);
    void SelectDeviceDlgID([In] int lFlags, [In, MarshalAs(UnmanagedType.BStr)] string bstrDeviceType, [In] int lDeviceType, [Out, MarshalAs(UnmanagedType.BStr)] out string pbstrDeviceID);
    void RegisterEventCallbackInterface([In] int lFlags, [In, MarshalAs(UnmanagedType.BStr)] string bstrDeviceID, [In] ref Guid pEventGUID, [In, MarshalAs(UnmanagedType.Interface)] IWiaEventCallback pIWiaEventCallback, [Out, MarshalAs(UnmanagedType.Interface)] out IUnknown pEventObject);
    void RegisterEventCallbackProgram([In] int lFlags, [In, MarshalAs(UnmanagedType.BStr)] string bstrDeviceID, [In] ref Guid pEventGUID, [In, MarshalAs(UnmanagedType.BStr)] string bstrCommandline, [In, MarshalAs(UnmanagedType.BStr)] string bstrName, [In, MarshalAs(UnmanagedType.BStr)] string bstrDescription, [In, MarshalAs(UnmanagedType.BStr)] string bstrIcon);
    void RegisterEventCallbackCLSID([In] int lFlags, [In, MarshalAs(UnmanagedType.BStr)] string bstrDeviceID, [In] ref Guid pEventGUID, [In] ref Guid pClsID, [In, MarshalAs(UnmanagedType.BStr)] string bstrName, [In, MarshalAs(UnmanagedType.BStr)] string bstrDescription, [In, MarshalAs(UnmanagedType.BStr)] string bstrIcon);
}

[ComImport]
[Guid("6CDD4503-ACF9-4020-B5B2-B26C3D4C7A94")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IWiaItem2
{
    void CreateChildItem([In] int lItemFlags, [In] int lCreationFlags, [In, MarshalAs(UnmanagedType.BStr)] string bstrItemName, [Out, MarshalAs(UnmanagedType.Interface)] out IWiaItem2 ppIWiaItem2);
    void DeleteItem([In] int lFlags);
    void EnumChildItems([In, MarshalAs(UnmanagedType.Interface)] IWiaItem2 pCategoryGUID, [Out, MarshalAs(UnmanagedType.Interface)] out IEnumWiaItem2 ppIEnumWiaItem2);
    void FindItemByName([In] int lFlags, [In, MarshalAs(UnmanagedType.BStr)] string bstrFullItemName, [Out, MarshalAs(UnmanagedType.Interface)] out IWiaItem2 ppIWiaItem2);
    void GetItemCategory([Out] out Guid pItemCategoryGUID);
    void GetItemType([Out] out int pItemType);
    void DeviceDlg([In] int lFlags, [In] IntPtr hwndParent, [In, MarshalAs(UnmanagedType.BStr)] string bstrFolderName, [In, MarshalAs(UnmanagedType.BStr)] string bstrFilename, [Out] out int plNumFiles, [Out, MarshalAs(UnmanagedType.SafeArray)] out string[] ppbstrFilePaths, [Out, MarshalAs(UnmanagedType.Interface)] out IWiaItem2 ppItemRoot);
    void DeviceCommand([In] int lFlags, [In] ref Guid pCmdGUID, [Out, MarshalAs(UnmanagedType.Interface)] out IWiaItem2 ppIWiaItem2);
    void GetRootItem([Out, MarshalAs(UnmanagedType.Interface)] out IWiaItem2 ppIWiaItem2);
    void EnumRegisterEventInfo([In] int lFlags, [In] ref Guid pEventGUID, [Out, MarshalAs(UnmanagedType.Interface)] out IEnumWiaEventInfo ppIEnum);
    void Diagnostic([In] int ulSize, [In] IntPtr pBuffer);
}

[ComImport]
[Guid("5E38B83C-8CF1-11D1-BF92-0060081ED811")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IEnumWIA_DEV_INFO
{
    void Next([In] uint celt, [Out, MarshalAs(UnmanagedType.Interface)] out IWiaPropertyStorage rgelt, [Out] out uint pceltFetched);
    void Skip([In] uint celt);
    void Reset();
    void Clone([Out, MarshalAs(UnmanagedType.Interface)] out IEnumWIA_DEV_INFO ppIEnum);
    void GetCount([Out] out uint celt);
}

[ComImport]
[Guid("5E8383FC-3391-11D2-9E54-00C04F68B221")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IEnumWiaItem2
{
    void Next([In] uint celt, [Out, MarshalAs(UnmanagedType.Interface)] out IWiaItem2 ppIWiaItem2, [Out] out uint pceltFetched);
    void Skip([In] uint celt);
    void Reset();
    void Clone([Out, MarshalAs(UnmanagedType.Interface)] out IEnumWiaItem2 ppIEnum);
    void GetCount([Out] out uint celt);
}

[ComImport]
[Guid("F7C1F2C1-DEBB-11D0-B5E7-00A0C90F26F7")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IWiaEventCallback
{
    void ImageEventCallback([In] ref Guid pEventGUID, [In, MarshalAs(UnmanagedType.BStr)] string bstrEventDescription, [In, MarshalAs(UnmanagedType.BStr)] string bstrDeviceID, [In, MarshalAs(UnmanagedType.BStr)] string bstrDeviceDescription, [In] int dwDeviceType, [In, MarshalAs(UnmanagedType.BStr)] string bstrFullItemName, [Out] out uint pulEventType, [Out] out uint pulReserved);
}

[ComImport]
[Guid("5E8383FC-3391-11D2-9E54-00C04F68B221")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IEnumWiaEventInfo
{
    void Next([In] uint celt, [Out, MarshalAs(UnmanagedType.Interface)] out IWiaEventInfo ppIWiaEventInfo, [Out] out uint pceltFetched);
    void Skip([In] uint celt);
    void Reset();
    void Clone([Out, MarshalAs(UnmanagedType.Interface)] out IEnumWiaEventInfo ppIEnum);
    void GetCount([Out] out uint celt);
}

[ComImport]
[Guid("A1F4E726-8CF1-11D1-BF92-0060081ED811")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IWiaEventInfo
{
    void GetEventGUID([Out] out Guid pguidEvent);
    void GetEventDescription([Out, MarshalAs(UnmanagedType.BStr)] out string pbstrEventDescription);
    void GetDeviceID([Out, MarshalAs(UnmanagedType.BStr)] out string pbstrDeviceID);
    void GetDeviceDescription([Out, MarshalAs(UnmanagedType.BStr)] out string pbstrDeviceDescription);
    void GetDeviceType([Out] out uint pulDeviceType);
    void GetDefaultHandler([Out, MarshalAs(UnmanagedType.BStr)] out string pbstrDefaultHandler);
}

[ComImport]
[Guid("98B5E8A0-29CC-491A-AAE7-7E8139DC1AF6")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IWiaPropertyStorage
{
    void ReadMultiple([In] uint cpspec, [In] IntPtr rgpspec, [Out] IntPtr rgpropvar);
    void WriteMultiple([In] uint cpspec, [In] IntPtr rgpspec, [In] IntPtr rgpropvar, [In] uint propidNameFirst);
    void DeleteMultiple([In] uint cpspec, [In] IntPtr rgpspec);
    void ReadPropertyNames([In] uint cpropid, [In] IntPtr rgpropid, [Out] IntPtr rglpwstrName);
    void WritePropertyNames([In] uint cpropid, [In] IntPtr rgpropid, [In] IntPtr rglpwstrName);
    void DeletePropertyNames([In] uint cpropid, [In] IntPtr rgpropid);
    void Commit([In] uint grfCommitFlags);
    void Revert();
    void Enum([Out, MarshalAs(UnmanagedType.Interface)] out IEnumSTATPROPSTG ppenum);
    void SetTimes([In] IntPtr pctime, [In] IntPtr patime, [In] IntPtr pmtime);
    void SetClass([In] ref Guid clsid);
    void Stat([Out] IntPtr pstatpsstg);
}

[ComImport]
[Guid("139F85C5-FE5E-11D0-BDB7-00C04FD706EC")]
[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
public interface IEnumSTATPROPSTG
{
    void Next([In] uint celt, [Out] IntPtr rgelt, [Out] out uint pceltFetched);
    void Skip([In] uint celt);
    void Reset();
    void Clone([Out, MarshalAs(UnmanagedType.Interface)] out IEnumSTATPROPSTG ppenum);
}

[ComImport]
[Guid("79C07CF1-CBDD-41EE-8EC3-F00080CADA7A")]
[CoClass(typeof(WiaDevMgr2Class))]
public interface WiaDevMgr2 : IWiaDevMgr2
{
}

[ComImport]
[Guid("79C07CF1-CBDD-41EE-8EC3-F00080CADA7A")]
public class WiaDevMgr2Class
{


    // WIA constants
    private const int WIA_DEVICE_TYPE_SCANNER = 1;
    private const int WIA_INTENT_IMAGE_TYPE_COLOR = 0x00000001;
    private const int WIA_INTENT_IMAGE_TYPE_GRAYSCALE = 0x00000002;
    private const int WIA_INTENT_MINIMIZE_SIZE = 0x00010000;
    private const int WIA_INTENT_MAXIMIZE_QUALITY = 0x00020000;

    public async Task<string> CaptureImageAsync()
    {
        return await Task.Run(() =>
        {
            try
            {
                Console.WriteLine("[DEBUG] Initializing WIA...");

                // Create WIA Device Manager
                var deviceManager = new WiaDevMgr2Class() as IWiaDevMgr2;
                if (deviceManager == null)
                {
                    Console.WriteLine("[DEBUG] Failed to create WIA Device Manager");
                    return null;
                }

                Console.WriteLine("[DEBUG] WIA Device Manager created");

                // Enumerate devices
                IEnumWIA_DEV_INFO deviceEnum;
                deviceManager.EnumDeviceInfo(WIA_DEVICE_TYPE_SCANNER, out deviceEnum);

                if (deviceEnum == null)
                {
                    Console.WriteLine("[DEBUG] No WIA devices found");
                    return null;
                }

                Console.WriteLine("[DEBUG] Found WIA devices, attempting to get first scanner...");

                // Get first device
                IWiaPropertyStorage deviceInfo;
                uint fetched;
                deviceEnum.Next(1, out deviceInfo, out fetched);

                if (fetched == 0 || deviceInfo == null)
                {
                    Console.WriteLine("[DEBUG] No scanner devices available");
                    return null;
                }

                Console.WriteLine("[DEBUG] Found scanner device, attempting to capture image...");

                // For simplicity, let's try using the device dialog
                string deviceId;
                IWiaItem2 rootItem;
                deviceManager.SelectDeviceDlgID(0, "ScannerDeviceType", WIA_DEVICE_TYPE_SCANNER, out deviceId);

                if (string.IsNullOrEmpty(deviceId))
                {
                    Console.WriteLine("[DEBUG] No device selected");
                    return null;
                }

                deviceManager.CreateDevice(0, deviceId, out rootItem);

                if (rootItem == null)
                {
                    Console.WriteLine("[DEBUG] Failed to create device");
                    return null;
                }

                // Use device dialog to capture image
                int numFiles;
                string[] filePaths;
                IWiaItem2 scannedItem;

                string tempFolder = Path.GetTempPath();
                string tempFileName = "wia_scan_" + DateTime.Now.Ticks + ".bmp";

                rootItem.DeviceDlg(0, IntPtr.Zero, tempFolder, tempFileName, out numFiles, out filePaths, out scannedItem);

                if (numFiles > 0 && filePaths != null && filePaths.Length > 0)
                {
                    string imagePath = filePaths[0];
                    Console.WriteLine($"[DEBUG] Image captured to: {imagePath}");

                    // Convert to Base64
                    if (File.Exists(imagePath))
                    {
                        byte[] imageBytes = File.ReadAllBytes(imagePath);
                        string base64 = Convert.ToBase64String(imageBytes);

                        // Clean up temp file
                        try { File.Delete(imagePath); } catch { }

                        return base64;
                    }
                }

                Console.WriteLine("[DEBUG] No image captured");
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] WIA Exception: {ex.Message}");
                return null;
            }
        });
    }
}

