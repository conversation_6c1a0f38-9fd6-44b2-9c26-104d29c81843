2025-07-15 16:26:15.824 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 16:26:15.940 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 16:26:15.951 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 16:26:15.963 +03:00 [INF] Capture image mode set to: false
2025-07-15 16:26:15.966 +03:00 [INF] Initializing hub connection...
2025-07-15 16:26:17.904 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\wwwroot. Static files may be unavailable.
2025-07-15 16:26:18.076 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 16:26:18.082 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 16:26:18.096 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 16:26:18.100 +03:00 [INF] Hosting environment: Production
2025-07-15 16:26:18.107 +03:00 [INF] Content root path: C:\Mis Agent
2025-07-15 16:26:18.327 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:26:18.403 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:26:18.436 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:26:18.445 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 123.5594ms
2025-07-15 16:26:20.523 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=k8SZWes8m4maQwtInuN3Gg - null null
2025-07-15 16:26:20.529 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 16:26:20.605 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 16:26:20.711 +03:00 [INF] Barcode initialized successfully.
2025-07-15 16:28:53.545 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 16:28:53.546 +03:00 [INF] COM ports populated.
2025-07-15 16:28:53.548 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 16:28:53.549 +03:00 [INF] TabPage returned successfully.
2025-07-15 16:29:00.032 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:29:00.036 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:29:00.037 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.3451ms
2025-07-15 16:29:00.041 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 16:29:00.043 +03:00 [INF] CORS policy execution successful.
2025-07-15 16:29:00.044 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:29:00.050 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 16:29:00.391 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 16:29:00.393 +03:00 [INF] Capture image mode set to: true
2025-07-15 16:29:00.393 +03:00 [INF] Initializing hub connection...
2025-07-15 16:29:02.428 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 16:29:02.430 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 16:29:02.431 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 16:29:02.432 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 4.0605ms
2025-07-15 16:29:02.441 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 16:29:02.458 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2405.0562ms
2025-07-15 16:29:02.460 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 16:29:02.461 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2419.7021ms
2025-07-15 16:29:04.462 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=uaf2lTO7YE23NsHTS7ca1Q - null null
2025-07-15 16:29:04.468 +03:00 [INF] Executing endpoint '/chatHub'
