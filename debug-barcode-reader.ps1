# Honeywell Barcode Reader Debug Script
# This script helps you debug and test your Honeywell barcode reader functionality

param(
    [string]$ComPort = "COM3",
    [string]$Mode = "interactive"
)

Write-Host "=== Honeywell Barcode Reader Debug Script ===" -ForegroundColor Green
Write-Host "COM Port: $ComPort" -ForegroundColor Yellow
Write-Host "Mode: $Mode" -ForegroundColor Yellow
Write-Host ""

# Check if .NET is available
try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ .NET is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install .NET 6.0 or later from https://dotnet.microsoft.com/download" -ForegroundColor Yellow
    exit 1
}

# Navigate to the project directory
$projectPath = "Plugin Agent Projects\Mis.Shared.Interface"
if (Test-Path $projectPath) {
    Set-Location $projectPath
    Write-Host "✓ Found project directory" -ForegroundColor Green
} else {
    Write-Host "✗ Project directory not found: $projectPath" -ForegroundColor Red
    exit 1
}

# Check if the debug console project exists
if (Test-Path "BarcodeDebugConsole.csproj") {
    Write-Host "✓ Found debug console project" -ForegroundColor Green
} else {
    Write-Host "✗ Debug console project not found" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Building debug console..." -ForegroundColor Yellow

# Build the project
try {
    dotnet build BarcodeDebugConsole.csproj --configuration Debug
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Build successful" -ForegroundColor Green
    } else {
        Write-Host "✗ Build failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ Build error: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Starting debug session..." -ForegroundColor Yellow
Write-Host "Press Ctrl+C to stop debugging" -ForegroundColor Gray
Write-Host ""

# Run the debug console
try {
    switch ($Mode.ToLower()) {
        "interactive" {
            dotnet run --project BarcodeDebugConsole.csproj -- $ComPort
        }
        "test" {
            Write-Host "Running automated tests..." -ForegroundColor Yellow
            # You can add automated test commands here
            dotnet run --project BarcodeDebugConsole.csproj -- $ComPort
        }
        default {
            Write-Host "Unknown mode: $Mode. Using interactive mode." -ForegroundColor Yellow
            dotnet run --project BarcodeDebugConsole.csproj -- $ComPort
        }
    }
} catch {
    Write-Host "✗ Runtime error: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Debug session completed." -ForegroundColor Green
