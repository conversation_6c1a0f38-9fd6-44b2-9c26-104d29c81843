using System;
using System.Threading.Tasks;

namespace Mis.Shared.Interface
{
    /// <summary>
    /// Console application for debugging Honeywell barcode reader functionality
    /// </summary>
    public class BarcodeDebugConsole
    {
        public static async Task Main(string[] args)
        {
            Console.WriteLine("=== Honeywell Barcode Reader Debug Console ===");
            Console.WriteLine("This tool helps debug barcode reader connectivity and image capture.");
            Console.WriteLine();

            string comPort = "COM3"; // Default
            
            // Parse command line arguments
            if (args.Length > 0)
            {
                comPort = args[0];
            }

            Console.WriteLine($"Using COM port: {comPort}");
            Console.WriteLine("Press any key to start debugging, or 'q' to quit...");
            
            var key = Console.ReadKey();
            if (key.KeyChar == 'q' || key.KeyChar == 'Q')
            {
                return;
            }

            Console.WriteLine();
            Console.WriteLine();

            try
            {
                await RunDebugSession(comPort);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Fatal error: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("Debug session completed. Press any key to exit...");
            Console.ReadKey();
        }

        private static async Task RunDebugSession(string comPort)
        {
            bool continueDebugging = true;

            while (continueDebugging)
            {
                Console.WriteLine();
                Console.WriteLine("=== Debug Menu ===");
                Console.WriteLine("1. Test COM port availability");
                Console.WriteLine("2. Test command generation");
                Console.WriteLine("3. Run simulated test");
                Console.WriteLine("4. Run interactive barcode reader test");
                Console.WriteLine("5. Test specific Honeywell model");
                Console.WriteLine("6. Change COM port");
                Console.WriteLine("q. Quit");
                Console.WriteLine();
                Console.Write("Select option: ");

                var input = Console.ReadLine();

                switch (input?.ToLower())
                {
                    case "1":
                        TestComPortAvailability();
                        break;
                    case "2":
                        TestCommandGeneration();
                        break;
                    case "3":
                        RunSimulatedTest();
                        break;
                    case "4":
                        await RunInteractiveTest(comPort);
                        break;
                    case "5":
                        await TestSpecificModel(comPort);
                        break;
                    case "6":
                        comPort = ChangeComPort();
                        break;
                    case "q":
                        continueDebugging = false;
                        break;
                    default:
                        Console.WriteLine("Invalid option. Please try again.");
                        break;
                }

                if (continueDebugging)
                {
                    Console.WriteLine();
                    Console.WriteLine("Press any key to continue...");
                    Console.ReadKey();
                }
            }
        }

        private static void TestComPortAvailability()
        {
            Console.WriteLine();
            Console.WriteLine("=== COM Port Availability Test ===");
            
            try
            {
                string[] availablePorts = System.IO.Ports.SerialPort.GetPortNames();
                Console.WriteLine($"Available COM ports: {string.Join(", ", availablePorts)}");
                
                if (availablePorts.Length == 0)
                {
                    Console.WriteLine("⚠️  No COM ports found. Check your device connections.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error checking COM ports: {ex.Message}");
            }
        }

        private static void TestCommandGeneration()
        {
            Console.WriteLine();
            Console.WriteLine("=== Command Generation Test ===");
            HoneywellBarcodeTest.TestCommandGeneration();
        }

        private static void RunSimulatedTest()
        {
            Console.WriteLine();
            Console.WriteLine("=== Simulated Test ===");
            HoneywellBarcodeTest.RunSimulatedTest();
        }

        private static async Task RunInteractiveTest(string comPort)
        {
            Console.WriteLine();
            Console.WriteLine("=== Interactive Barcode Reader Test ===");
            Console.WriteLine("Make sure your barcode reader is connected and ready.");
            Console.WriteLine("Press any key to start the test...");
            Console.ReadKey();
            
            await HoneywellBarcodeTest.RunInteractiveDebugTest(comPort);
        }

        private static async Task TestSpecificModel(string comPort)
        {
            Console.WriteLine();
            Console.WriteLine("=== Test Specific Model ===");
            Console.WriteLine("1. Honeywell 1900");
            Console.WriteLine("2. Honeywell 1950");
            Console.WriteLine("3. Auto-detect");
            Console.Write("Select model: ");

            var input = Console.ReadLine();
            HoneywellModel model = input switch
            {
                "1" => HoneywellModel.Model1900,
                "2" => HoneywellModel.Model1950,
                "3" => HoneywellModel.Auto,
                _ => HoneywellModel.Auto
            };

            Console.WriteLine($"Testing with {model}...");
            
            try
            {
                var serialManager = SerialPortManager.Instance;
                
                Console.WriteLine("Starting serial port...");
                serialManager.StartListening(comPort);
                
                if (serialManager.IsPortOpen)
                {
                    Console.WriteLine("✓ Serial port opened");
                    
                    Console.WriteLine($"Sending capture command for {model}...");
                    await serialManager.CaptureImageAsync(comPort, model);
                    
                    Console.WriteLine("Waiting for response (10 seconds)...");
                    await Task.Delay(10000);
                    
                    string result = serialManager.LastScannedBase64Data;
                    if (!string.IsNullOrEmpty(result))
                    {
                        Console.WriteLine($"✓ SUCCESS! Received Base64 data (length: {result.Length})");
                    }
                    else
                    {
                        Console.WriteLine("✗ No data received");
                    }
                }
                else
                {
                    Console.WriteLine("✗ Failed to open serial port");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error: {ex.Message}");
            }
            finally
            {
                SerialPortManager.Instance.StopListening();
            }
        }

        private static string ChangeComPort()
        {
            Console.WriteLine();
            Console.WriteLine("=== Change COM Port ===");
            Console.Write("Enter new COM port (e.g., COM3): ");
            string newPort = Console.ReadLine();
            
            if (string.IsNullOrWhiteSpace(newPort))
            {
                Console.WriteLine("Invalid port. Keeping current setting.");
                return "COM3";
            }
            
            Console.WriteLine($"COM port changed to: {newPort}");
            return newPort.Trim().ToUpper();
        }
    }
}
