2025-07-15 15:56:10.194 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 15:56:10.339 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-15 15:56:10.354 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-15 15:56:10.366 +03:00 [INF] Capture image mode set to: false
2025-07-15 15:56:10.371 +03:00 [INF] Initializing hub connection...
2025-07-15 15:56:13.018 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\wwwroot. Static files may be unavailable.
2025-07-15 15:56:13.136 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 15:56:13.144 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 15:56:13.155 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:56:13.161 +03:00 [INF] Hosting environment: Production
2025-07-15 15:56:13.165 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent
2025-07-15 15:56:13.257 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:56:13.327 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:56:13.373 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:56:13.384 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 129.9426ms
2025-07-15 15:56:15.458 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=86qWA8SCveOMWz6UjNXOgw - null null
2025-07-15 15:56:15.471 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:56:15.589 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-15 15:56:15.659 +03:00 [INF] Barcode initialized successfully.
2025-07-15 15:56:24.521 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:56:24.526 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:56:24.528 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 6.185ms
2025-07-15 15:56:24.531 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:56:24.533 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:56:24.534 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:56:24.541 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 15:56:32.649 +03:00 [INF] Executing ObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 15:56:32.667 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 8122.83ms
2025-07-15 15:56:32.669 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:56:32.670 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 500 null application/json; charset=utf-8 8138.701ms
2025-07-15 15:56:36.189 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 15:56:36.191 +03:00 [INF] COM ports populated.
2025-07-15 15:56:37.311 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-15 15:56:37.312 +03:00 [INF] TabPage returned successfully.
2025-07-15 15:56:46.001 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 15:56:46.034 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 15:56:46.037 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 15:56:46.040 +03:00 [INF] Capture image mode set to: false
2025-07-15 15:56:46.042 +03:00 [INF] Initializing hub connection...
2025-07-15 15:57:13.857 +03:00 [INF] Starting Barcode Initialization...
2025-07-15 15:57:13.934 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM4
2025-07-15 15:57:13.942 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: false
2025-07-15 15:57:13.949 +03:00 [INF] Capture image mode set to: false
2025-07-15 15:57:13.951 +03:00 [INF] Initializing hub connection...
2025-07-15 15:57:15.574 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\wwwroot. Static files may be unavailable.
2025-07-15 15:57:15.639 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-15 15:57:15.645 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-15 15:57:15.650 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-15 15:57:15.653 +03:00 [INF] Hosting environment: Production
2025-07-15 15:57:15.655 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent
2025-07-15 15:57:16.240 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:57:16.271 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:57:16.283 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:57:16.286 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 50.1028ms
2025-07-15 15:57:16.296 +03:00 [INF] Building Barcode TabPage UI.
2025-07-15 15:57:16.323 +03:00 [INF] COM ports populated.
2025-07-15 15:57:17.455 +03:00 [INF] Barcode scanner port set to: COM4
2025-07-15 15:57:17.460 +03:00 [INF] TabPage returned successfully.
2025-07-15 15:57:18.344 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=l8eHbDdUJlFCUPdOs7sAow - null null
2025-07-15 15:57:18.351 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:57:18.421 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 15:57:18.491 +03:00 [INF] Barcode initialized successfully.
2025-07-15 15:57:31.794 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:57:31.808 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:57:31.813 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 19.128ms
2025-07-15 15:57:31.824 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:57:31.832 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:57:31.835 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:57:31.852 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 15:57:32.344 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 15:57:32.348 +03:00 [INF] Capture image mode set to: true
2025-07-15 15:57:32.349 +03:00 [INF] Initializing hub connection...
2025-07-15 15:57:34.387 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:57:34.389 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:57:34.390 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:57:34.391 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 4.4654ms
2025-07-15 15:57:34.392 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 15:57:34.410 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2550.2508ms
2025-07-15 15:57:34.411 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:57:34.412 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2588.1932ms
2025-07-15 15:57:36.424 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=cfsW3FqZbaO2VZg1R-kyrA - null null
2025-07-15 15:57:36.428 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:57:44.729 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:57:44.731 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:57:44.731 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.7501ms
2025-07-15 15:57:44.733 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:57:44.735 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:57:44.736 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:57:44.737 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 15:57:44.739 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 15:57:44.740 +03:00 [INF] Capture image mode set to: true
2025-07-15 15:57:44.740 +03:00 [INF] Initializing hub connection...
2025-07-15 15:57:46.772 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:57:46.775 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:57:46.776 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:57:46.776 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 15:57:46.776 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 3.8935ms
2025-07-15 15:57:46.777 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2039.0033ms
2025-07-15 15:57:46.780 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:57:46.780 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2047.4271ms
2025-07-15 15:57:48.809 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=eebAw-B_2FSTZ-eVerSxbA - null null
2025-07-15 15:57:48.815 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:57:48.818 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 15:57:51.073 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:57:51.074 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:57:51.075 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.6665ms
2025-07-15 15:57:51.078 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:57:51.079 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:57:51.080 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:57:51.081 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 15:57:51.082 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 15:57:51.083 +03:00 [INF] Capture image mode set to: true
2025-07-15 15:57:51.084 +03:00 [INF] Initializing hub connection...
2025-07-15 15:57:53.115 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:57:53.117 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 15:57:53.120 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:57:53.122 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2039.6169ms
2025-07-15 15:57:53.123 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:57:53.124 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:57:53.126 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 10.1702ms
2025-07-15 15:57:53.126 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2048.6839ms
2025-07-15 15:57:55.152 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=zwm1wpxngmfQoNIakJiMMQ - null null
2025-07-15 15:57:55.156 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:57:55.158 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 15:58:52.557 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:58:52.559 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:58:52.560 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.1119ms
2025-07-15 15:58:52.562 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:58:52.567 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:58:52.569 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:58:52.569 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 15:58:52.571 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 15:58:52.571 +03:00 [INF] Capture image mode set to: true
2025-07-15 15:58:52.572 +03:00 [INF] Initializing hub connection...
2025-07-15 15:58:54.601 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:58:54.602 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:58:54.603 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:58:54.604 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 3.4308ms
2025-07-15 15:58:54.605 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 15:58:54.607 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2036.8339ms
2025-07-15 15:58:54.609 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:58:54.610 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2048.0866ms
2025-07-15 15:58:56.618 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=1NXotBen8V6TxUshsaalew - null null
2025-07-15 15:58:56.623 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:58:56.625 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 15:59:22.584 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:59:22.586 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:59:22.586 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 2.342ms
2025-07-15 15:59:22.588 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:59:22.591 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:59:22.592 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:59:22.594 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 15:59:22.595 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 15:59:22.596 +03:00 [INF] Capture image mode set to: true
2025-07-15 15:59:22.597 +03:00 [INF] Initializing hub connection...
2025-07-15 15:59:24.623 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:59:24.625 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:59:24.626 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:59:24.627 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 3.492ms
2025-07-15 15:59:24.628 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 15:59:24.630 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2034.2694ms
2025-07-15 15:59:24.630 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:59:24.632 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2043.177ms
2025-07-15 15:59:26.647 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=MaaCNqP1VIbrO0N6HR6Puw - null null
2025-07-15 15:59:26.650 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:59:26.652 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
2025-07-15 15:59:35.533 +03:00 [INF] Request starting HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:59:35.537 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:59:35.538 +03:00 [INF] Request finished HTTP/2 OPTIONS https://localhost:7000/Barcode/ScanAsync - 204 null null 5.4297ms
2025-07-15 15:59:35.541 +03:00 [INF] Request starting HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - null null
2025-07-15 15:59:35.543 +03:00 [INF] CORS policy execution successful.
2025-07-15 15:59:35.544 +03:00 [INF] Executing endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:59:35.545 +03:00 [INF] Route matched with {action = "Scan", controller = "Barcode"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.IActionResult] ScanAsync() on controller Mis.Agent.Barcode.BarcodeController (Mis.Agent.Barcode).
2025-07-15 15:59:35.547 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM4, CaptureMode: true
2025-07-15 15:59:35.548 +03:00 [INF] Capture image mode set to: true
2025-07-15 15:59:35.550 +03:00 [INF] Initializing hub connection...
2025-07-15 15:59:37.581 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-15 15:59:37.584 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-15 15:59:37.585 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-15 15:59:37.586 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 4.2169ms
2025-07-15 15:59:37.588 +03:00 [INF] Executing BadRequestObjectResult, writing value of type '<>f__AnonymousType0`2[[System.Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]'.
2025-07-15 15:59:37.589 +03:00 [INF] Executed action Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode) in 2041.835ms
2025-07-15 15:59:37.589 +03:00 [INF] Executed endpoint 'Mis.Agent.Barcode.BarcodeController.ScanAsync (Mis.Agent.Barcode)'
2025-07-15 15:59:37.590 +03:00 [INF] Request finished HTTP/2 GET https://localhost:7000/Barcode/ScanAsync - 400 null application/json; charset=utf-8 2049.4115ms
2025-07-15 15:59:39.618 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=8SzyNcbjxmPa70FtzLUfcQ - null null
2025-07-15 15:59:39.623 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-15 15:59:39.625 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM4
