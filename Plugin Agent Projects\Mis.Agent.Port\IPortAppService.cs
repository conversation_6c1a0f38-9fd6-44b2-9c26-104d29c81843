﻿using Mis.Shared.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Port
{
    public interface IPortAppService 
    {
        void UpdateBaseUrl(string newBaseUrl);
        void UpdateBarcodeBaseUrl(string newBaseUrl);
        void UpdateCOMPort(string selectedComPort);
        bool IsValidBarcodeUrl(string url);
        void ShowNotification(string title, string text);
        string GetCOMPort();
        string GetBarcodeBaseUrl();
        string GetBaseUrl();
    }
}
