﻿using Mis.Shared.Interface;
using Mis.Agent.Barcode.Services;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Mis.Agent.Barcode
{
    [ApiController]
    [Route("[controller]")]
    [EnableCors("AllowAll")]
    public class BarcodeController : ControllerBase
    {
        private readonly IBarcodeAppService _barcodeAppService;
        //private readonly IPublicBarcodeAppService _publicBarcodeAppService;
        private readonly IScannerAppService _scannerAppService;
        public BarcodeController(IBarcodeAppService barcodeAppService,
            IScannerAppService scannerAppService)
        {
            _barcodeAppService = barcodeAppService;
            _scannerAppService = scannerAppService;
        }

        [HttpGet("ScanAsync")]
        public async Task<IActionResult> ScanAsync()
        {
            try
            {
                var setting = _scannerAppService.GetSetting("IsScanByBarcodeReader");
                bool.TryParse(setting, out bool isScanByBarcodeReader);

                if (isScanByBarcodeReader)
                    return await ScanUsingBarcodeReader();
                else
                    return ScanUsingScannerDevice();
            }
            catch (UnauthorizedAccessException)
            {
                return StatusCode(403, new { success = false, message = "Access denied to the COM port." });
            }
            catch (IOException ex)
            {
                return StatusCode(500, new { success = false, message = "I/O error occurred: " + ex.Message });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }

        private async Task<IActionResult> ScanUsingBarcodeReader()
        {
            string comPort = _scannerAppService.GetCOMPort();
            string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();
            HoneywellModel model = _scannerAppService.GetHoneywellModelEnum();

            _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);

            await SerialPortManager.Instance.CaptureImageAsync(comPort, model);
            Thread.Sleep(2000);

            string scannedData = SerialPortManager.Instance.LastScannedBase64Data;
            SerialPortManager.Instance.LastScannedBase64Data = null;

            if (string.IsNullOrEmpty(scannedData))
            {
                _barcodeAppService.ShowNotification("Barcode Notification", "Scanning failed. No image data received.");
                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
            }

            _barcodeAppService.ShowNotification("Barcode Notification", "Barcode Capture Image Successfully");
            return Ok(new { success = true, data = scannedData });
        }

        private IActionResult ScanUsingScannerDevice()
        {
            string selectedScanner = _scannerAppService.GetSetting("Scanner");

            if (string.IsNullOrEmpty(selectedScanner))
            {
                return BadRequest(new { success = false, message = "No scanner is configured in the appsettings.json file." });
            }

            var scanners = _scannerAppService.GetAvailableScanners();
            if (scanners == null || !scanners.Any())
            {
                return BadRequest(new { success = false, message = "No scanners found." });
            }

            string scannedData = SerialPortManager.Instance.ScanWithSelectedScanner(selectedScanner);

            if (string.IsNullOrEmpty(scannedData))
            {
                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
            }

            return Ok(new { success = true, data = scannedData });
        }

        [HttpGet("DebugBarcodeReader")]
        public async Task<IActionResult> DebugBarcodeReader()
        {
            try
            {
                Console.WriteLine("=== DEBUG: Starting Barcode Reader Debug Session ===");

                string comPort = _scannerAppService.GetCOMPort();
                string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();
                HoneywellModel model = _scannerAppService.GetHoneywellModelEnum();

                Console.WriteLine($"[DEBUG] Configuration:");
                Console.WriteLine($"[DEBUG] - COM Port: {comPort}");
                Console.WriteLine($"[DEBUG] - Barcode URL: {barcodeUrl}");
                Console.WriteLine($"[DEBUG] - Honeywell Model: {model}");

                // Test 1: Check COM port availability
                Console.WriteLine($"\n[DEBUG] Step 1: Checking COM port availability...");
                string[] availablePorts = System.IO.Ports.SerialPort.GetPortNames();
                Console.WriteLine($"[DEBUG] Available ports: {string.Join(", ", availablePorts)}");

                if (!availablePorts.Contains(comPort))
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = $"COM port {comPort} not available. Available ports: {string.Join(", ", availablePorts)}"
                    });
                }

                // Test 2: Initialize barcode service
                Console.WriteLine($"\n[DEBUG] Step 2: Initializing barcode service...");
                _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);

                // Test 3: Check if serial port opens
                Console.WriteLine($"\n[DEBUG] Step 3: Checking serial port connection...");
                if (!SerialPortManager.Instance.IsPortOpen)
                {
                    return BadRequest(new
                    {
                        success = false,
                        message = "Failed to open serial port connection"
                    });
                }

                Console.WriteLine($"[DEBUG] ✓ Serial port opened successfully");

                // Test 4: Try multiple capture attempts for Honeywell 1900
                Console.WriteLine($"\n[DEBUG] Step 4: Attempting image capture with multiple methods...");

                string scannedData = null;
                string[] modelsToTry = { "Model1900", "Model1950", "Auto" };

                foreach (string modelName in modelsToTry)
                {
                    Console.WriteLine($"\n[DEBUG] Trying with model: {modelName}");

                    HoneywellModel testModel = modelName switch
                    {
                        "Model1900" => HoneywellModel.Model1900,
                        "Model1950" => HoneywellModel.Model1950,
                        _ => HoneywellModel.Auto
                    };

                    // Try standard command first
                    Console.WriteLine($"[DEBUG] Attempting standard command for {modelName}...");
                    SerialPortManager.Instance.LastScannedBase64Data = null;
                    await SerialPortManager.Instance.CaptureImageAsync(comPort, testModel);
                    await Task.Delay(3000);
                    scannedData = SerialPortManager.Instance.LastScannedBase64Data;

                    if (!string.IsNullOrEmpty(scannedData))
                    {
                        Console.WriteLine($"[DEBUG] ✓ SUCCESS with standard {modelName} command!");
                        break;
                    }

                    // If Model1900 and no success, try alternative command
                    if (testModel == HoneywellModel.Model1900)
                    {
                        Console.WriteLine($"[DEBUG] Trying alternative command for {modelName}...");
                        SerialPortManager.Instance.LastScannedBase64Data = null;

                        // Send alternative command manually
                        string altCmd = imgCapture.GetAlternativePictureCmdForModel(testModel);
                        string command = "\x16M\r" + altCmd;
                        Console.WriteLine($"[DEBUG] Alternative command: {altCmd}");
                        SerialPortManager.Instance.Write(command);

                        await Task.Delay(3000);
                        scannedData = SerialPortManager.Instance.LastScannedBase64Data;

                        if (!string.IsNullOrEmpty(scannedData))
                        {
                            Console.WriteLine($"[DEBUG] ✓ SUCCESS with alternative {modelName} command!");
                            break;
                        }
                    }

                    Console.WriteLine($"[DEBUG] ✗ No success with {modelName}");
                }

                if (string.IsNullOrEmpty(scannedData))
                {
                    Console.WriteLine($"[DEBUG] ✗ All attempts failed - no image data received");
                    return BadRequest(new
                    {
                        success = false,
                        message = "No image data received after trying all models. The device may need different configuration or commands."
                    });
                }

                Console.WriteLine($"[DEBUG] Final result: Base64 data length: {scannedData.Length}");
                Console.WriteLine($"[DEBUG] First 100 characters: {scannedData.Substring(0, Math.Min(100, scannedData.Length))}...");

                return Ok(new
                {
                    success = true,
                    data = scannedData,
                    message = $"Successfully captured image. Check console for detailed debug logs.",
                    debugInfo = new
                    {
                        comPort = comPort,
                        finalModel = "Auto-detected",
                        dataLength = scannedData.Length
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] ✗ Exception: {ex.Message}");
                Console.WriteLine($"[DEBUG] Stack trace: {ex.StackTrace}");
                return StatusCode(500, new
                {
                    success = false,
                    message = ex.Message,
                    stackTrace = ex.StackTrace
                });
            }
        }

        [HttpGet("CaptureImageTWAIN")]
        public async Task<IActionResult> CaptureImageTWAIN()
        {
            try
            {
                Console.WriteLine("=== DEBUG: Starting TWAIN Image Capture ===");

                var twainService = new TwainImageCaptureService();
                string base64Image = await twainService.CaptureImageAsync();

                if (!string.IsNullOrEmpty(base64Image))
                {
                    Console.WriteLine($"[DEBUG] ✓ SUCCESS! Captured image via TWAIN (length: {base64Image.Length})");
                    return Ok(new
                    {
                        success = true,
                        data = base64Image,
                        message = "Successfully captured image using TWAIN",
                        captureMethod = "TWAIN"
                    });
                }
                else
                {
                    Console.WriteLine("[DEBUG] ✗ No image captured via TWAIN");
                    return BadRequest(new
                    {
                        success = false,
                        message = "Failed to capture image using TWAIN. Check if scanner is TWAIN compatible."
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] TWAIN Exception: {ex.Message}");
                return StatusCode(500, new
                {
                    success = false,
                    message = ex.Message,
                    captureMethod = "TWAIN"
                });
            }
        }

        [HttpGet("CaptureImageWIA")]
        public async Task<IActionResult> CaptureImageWIA()
        {
            try
            {
                Console.WriteLine("=== DEBUG: Starting WIA Image Capture ===");

                var wiaService = new WiaImageCaptureService();
                string base64Image = await wiaService.CaptureImageAsync();

                if (!string.IsNullOrEmpty(base64Image))
                {
                    Console.WriteLine($"[DEBUG] ✓ SUCCESS! Captured image via WIA (length: {base64Image.Length})");
                    return Ok(new
                    {
                        success = true,
                        data = base64Image,
                        message = "Successfully captured image using WIA",
                        captureMethod = "WIA"
                    });
                }
                else
                {
                    Console.WriteLine("[DEBUG] ✗ No image captured via WIA");
                    return BadRequest(new
                    {
                        success = false,
                        message = "Failed to capture image using WIA. Check if scanner is WIA compatible."
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[DEBUG] WIA Exception: {ex.Message}");
                return StatusCode(500, new
                {
                    success = false,
                    message = ex.Message,
                    captureMethod = "WIA"
                });
            }
        }

        //[HttpGet("ScanAsync")]
        //public async Task<IActionResult> ScanAsync()
        //{
        //    try
        //    {
        //        //Retrieve the value of IsScanByBarcodeReader from the configuration(appsettings.json)
        //        bool isScanByBarcodeReader = bool.Parse(_scannerAppService.GetSetting("IsScanByBarcodeReader"));
        //        if (isScanByBarcodeReader)
        //        {
        //            //Barcode Reader logic
        //            string comPort = _scannerAppService.GetCOMPort();
        //            string barcodeUrl = _scannerAppService.GetBarcodeBaseUrl();
        //            // Initialize the barcode service
        //            _barcodeAppService.PublicInitialize(barcodeUrl, comPort, true);
        //            // Capture the image
        //           await SerialPortManager.Instance.CaptureImageAsync(comPort);
        //             Thread.Sleep(2000);

        //            string scannedData = SerialPortManager.Instance.LastScannedBase64Data;

        //            SerialPortManager.Instance.LastScannedBase64Data = null;
        //            if (string.IsNullOrEmpty(scannedData))
        //            {
        //                _barcodeAppService.ShowNotification("Barcode Notification", "Scanning failed. No image data received.");
        //                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
        //            }

        //            _barcodeAppService.ShowNotification("Barcode Notification", "Barcode Capture Image Successfully");
        //            return Ok(new { success = true, data = scannedData });
        //        }
        //        else
        //        {
        //            //Non - barcode scanner logic: Get the scanner name from appsettings

        //            string selectedScanner = _scannerAppService.GetSetting("Scanner");

        //            if (string.IsNullOrEmpty(selectedScanner))
        //            {
        //                return BadRequest(new { success = false, message = "No scanner is configured in the appsettings.json file." });
        //            }

        //            //Scanner logic
        //            var scanners = _scannerAppService.GetAvailableScanners();
        //            if (scanners == null || !scanners.Any())
        //            {
        //                return BadRequest(new { success = false, message = "No scanners found." });
        //            }

        //            //Use the selected scanner to perform scanning
        //            string scannedData = SerialPortManager.Instance.ScanWithSelectedScanner(selectedScanner);

        //            if (string.IsNullOrEmpty(scannedData))
        //            {
        //                return BadRequest(new { success = false, message = "Scanning failed. No image data received." });
        //            }

        //            return Ok(new { success = true, data = scannedData });
        //        }
        //        return Ok(new { success = true });
        //    }
        //    catch (UnauthorizedAccessException ex)
        //    {
        //        return StatusCode(403, new { success = false, message = "Access denied to the COM port." });
        //    }
        //    catch (IOException ex)
        //    {
        //        return StatusCode(500, new { success = false, message = "I/O error occurred: " + ex.Message });
        //    }
        //    catch (Exception ex)
        //    {
        //        return StatusCode(500, new { success = false, message = ex.Message });
        //    }
        //}
    }
}
