{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/Mis.Agent/Mis.Agent.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile", "group": {"kind": "build", "isDefault": true}}, {"label": "publish", "command": "dotnet", "type": "process", "args": ["publish", "${workspaceFolder}/Mis.Agent/Mis.Agent.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}, {"label": "watch", "command": "dotnet", "type": "process", "args": ["watch", "run", "--project", "${workspaceFolder}/Mis.Agent/Mis.Agent.csproj"], "problemMatcher": "$msCompile"}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/Mis.Agent/Mis.Agent.csproj", "/property:GenerateFullPaths=true", "/consoleloggerparameters:NoSummary"], "problemMatcher": "$msCompile"}]}