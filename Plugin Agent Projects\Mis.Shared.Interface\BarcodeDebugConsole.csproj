<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <StartupObject>Mis.Shared.Interface.BarcodeDebugConsole</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="System.Drawing.Common" Version="6.0.0" />
    <PackageReference Include="System.IO.Ports" Version="6.0.0" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="6.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="6.0.0" />
    <PackageReference Include="System.Data.SQLite" Version="1.0.118" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Interop.WIA">
      <HintPath>WIA\Interop.WIA.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>