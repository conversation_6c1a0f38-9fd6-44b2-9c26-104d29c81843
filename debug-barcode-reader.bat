@echo off
echo === Honeywell Barcode Reader Debug Script ===
echo.

REM Set default COM port
set COM_PORT=COM3
if not "%1"=="" set COM_PORT=%1

echo Using COM Port: %COM_PORT%
echo.

REM Check if .NET is available
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo Error: .NET is not installed or not in PATH
    echo Please install .NET 6.0 or later from https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo .NET is available
echo.

REM Navigate to project directory
cd "Plugin Agent Projects\Mis.Shared.Interface"
if errorlevel 1 (
    echo Error: Could not find project directory
    pause
    exit /b 1
)

echo Found project directory
echo.

REM Check if debug console project exists
if not exist "BarcodeDebugConsole.csproj" (
    echo Error: Debug console project not found
    pause
    exit /b 1
)

echo Building debug console...
dotnet build BarcodeDebugConsole.csproj --configuration Debug
if errorlevel 1 (
    echo Build failed
    pause
    exit /b 1
)

echo Build successful
echo.
echo Starting debug session...
echo Press Ctrl+C to stop debugging
echo.

REM Run the debug console
dotnet run --project BarcodeDebugConsole.csproj -- %COM_PORT%

echo.
echo Debug session completed.
pause
